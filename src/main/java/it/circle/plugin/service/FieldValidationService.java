package it.circle.plugin.service;

import it.circle.plugin.utility.FieldMappingUtility;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class FieldValidationService {
    
    private final FieldMetadataService fieldMetadataService;
    
    /**
     * Validate requested fields against available fields for an entity
     * Returns a validation result with valid fields, invalid fields, and suggestions
     */
    public FieldValidationResult validateFields(List<String> requestedFields, String entityType, String instanceId) {
        if (requestedFields == null || requestedFields.isEmpty()) {
            return new FieldValidationResult(Collections.emptyList(), Collections.emptyList(), Collections.emptyMap());
        }
        
        // Get available fields from SettingsEntity
        List<String> availableFields = fieldMetadataService.getEntityFields(entityType, instanceId);
        
        List<String> validFields = new ArrayList<>();
        List<String> invalidFields = new ArrayList<>();
        Map<String, String> fieldSuggestions = new HashMap<>();
        
        for (String requestedField : requestedFields) {
            // Try to find exact or alias match
            Optional<String> matchingField = FieldMappingUtility.findBestMatchingField(requestedField, availableFields);
            
            if (matchingField.isPresent()) {
                validFields.add(requestedField); // Keep original field name
                if (!requestedField.equals(matchingField.get())) {
                    fieldSuggestions.put(requestedField, matchingField.get());
                }
            } else {
                invalidFields.add(requestedField);
                // Try to suggest similar fields
                String suggestion = findSimilarField(requestedField, availableFields);
                if (suggestion != null) {
                    fieldSuggestions.put(requestedField, suggestion);
                }
            }
        }
        
        return new FieldValidationResult(validFields, invalidFields, fieldSuggestions);
    }
    
    /**
     * Find similar field names using simple string similarity
     */
    private String findSimilarField(String requestedField, List<String> availableFields) {
        String lowerRequested = requestedField.toLowerCase();
        
        // Look for fields that contain the requested field or vice versa
        for (String availableField : availableFields) {
            String lowerAvailable = availableField.toLowerCase();
            if (lowerAvailable.contains(lowerRequested) || lowerRequested.contains(lowerAvailable)) {
                return availableField;
            }
        }
        
        // Look for fields with similar prefixes or suffixes
        for (String availableField : availableFields) {
            String lowerAvailable = availableField.toLowerCase();
            if (haveSimilarParts(lowerRequested, lowerAvailable)) {
                return availableField;
            }
        }
        
        return null;
    }
    
    /**
     * Check if two field names have similar parts (simple heuristic)
     */
    private boolean haveSimilarParts(String field1, String field2) {
        String[] parts1 = field1.split("[_\\s]+");
        String[] parts2 = field2.split("[_\\s]+");
        
        for (String part1 : parts1) {
            for (String part2 : parts2) {
                if (part1.length() > 2 && part2.length() > 2 && 
                    (part1.contains(part2) || part2.contains(part1))) {
                    return true;
                }
            }
        }
        
        return false;
    }
    
    /**
     * Get all available fields for an entity with their metadata
     */
    public List<String> getAvailableFields(String entityType, String instanceId) {
        return fieldMetadataService.getEntityFields(entityType, instanceId);
    }
    
    /**
     * Result class for field validation
     */
    public static class FieldValidationResult {
        private final List<String> validFields;
        private final List<String> invalidFields;
        private final Map<String, String> fieldSuggestions;
        
        public FieldValidationResult(List<String> validFields, List<String> invalidFields, Map<String, String> fieldSuggestions) {
            this.validFields = validFields;
            this.invalidFields = invalidFields;
            this.fieldSuggestions = fieldSuggestions;
        }
        
        public List<String> getValidFields() {
            return validFields;
        }
        
        public List<String> getInvalidFields() {
            return invalidFields;
        }
        
        public Map<String, String> getFieldSuggestions() {
            return fieldSuggestions;
        }
        
        public boolean hasInvalidFields() {
            return !invalidFields.isEmpty();
        }
        
        public boolean hasValidFields() {
            return !validFields.isEmpty();
        }
        
        public String getValidationSummary() {
            StringBuilder summary = new StringBuilder();
            
            if (hasValidFields()) {
                summary.append("Valid fields: ").append(validFields);
            }
            
            if (hasInvalidFields()) {
                if (summary.length() > 0) {
                    summary.append("; ");
                }
                summary.append("Invalid fields: ").append(invalidFields);
                
                if (!fieldSuggestions.isEmpty()) {
                    summary.append("; Suggestions: ");
                    fieldSuggestions.entrySet().forEach(entry -> 
                        summary.append(entry.getKey()).append("->").append(entry.getValue()).append(" "));
                }
            }
            
            return summary.toString();
        }
    }
}
