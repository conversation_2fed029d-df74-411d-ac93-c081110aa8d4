package it.circle.plugin.service;

import it.circle.crm.api.settings.entity.model.EntityList;
import it.circle.crm.data.settings.entity.document.SettingsEntityDocument;
import it.circle.plugin.model.FieldMetadata;
import it.circle.plugin.repository.SettingsEntityRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class FieldMetadataService {
    
    private final SettingsEntityRepository settingsEntityRepository;
    
    /**
     * Get all available fields for an entity from SettingsEntity collection
     * This method is cached to improve performance
     */
    @Cacheable(value = "entityFields", key = "#entityType + '_' + #instanceId")
    public List<String> getEntityFields(String entityType, String instanceId) {
        log.debug("Retrieving fields for entity: {} in instance: {}", entityType, instanceId);
        
        try {
            // First try to find entity-specific settings for this instance
            Optional<SettingsEntityDocument> settingsOpt =
                settingsEntityRepository.findByEntityAndInstanceId(entityType, instanceId);

            if (settingsOpt.isEmpty()) {
                // Fallback to global entity settings
                List<SettingsEntityDocument> globalSettings =
                    settingsEntityRepository.findByEntity(entityType);
                if (!globalSettings.isEmpty()) {
                    settingsOpt = Optional.of(globalSettings.get(0));
                }
            }
            
            if (settingsOpt.isPresent()) {
                SettingsEntityDocument settings = settingsOpt.get();
                List<String> fields = new ArrayList<>();

                // Extract fields from EntityList (list view fields) - this contains the exportable fields
                if (settings.getList() != null) {
                    fields.addAll(extractFieldsFromEntityList(settings.getList()));
                }

                // Remove duplicates and return
                List<String> uniqueFields = fields.stream()
                    .distinct()
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

                log.info("Found {} fields for entity {} in instance {}: {}",
                    uniqueFields.size(), entityType, instanceId, uniqueFields);

                return uniqueFields;
            }
            
        } catch (Exception e) {
            log.error("Error retrieving fields for entity {} in instance {}: {}", 
                entityType, instanceId, e.getMessage(), e);
        }
        
        // Fallback to hard-coded default fields if no settings found
        log.warn("No settings found for entity {}, using fallback default fields", entityType);
        return getFallbackDefaultFields(entityType);
    }
    

    
    /**
     * Extract field names from EntityList configuration
     */
    private List<String> extractFieldsFromEntityList(EntityList entityList) {
        List<String> fields = new ArrayList<>();
        
        if (entityList.getFields() != null) {
            for (Object field : entityList.getFields()) {
                try {
                    // Use reflection to get field name
                    String fieldName = extractFieldProperty(field, "field");
                    if (fieldName != null && !fieldName.trim().isEmpty()) {
                        fields.add(fieldName);
                    }
                } catch (Exception e) {
                    log.debug("Could not extract field name from field object: {}", field);
                }
            }
        }
        
        return fields;
    }
    
    /**
     * Get default field list as fallback if SettingsEntity is not available
     */
    private List<String> getFallbackDefaultFields(String entityType) {
        return switch (entityType) {
            case "Order" -> Arrays.asList("customer", "order_id", "date", "status", "total_paid", "payment_method", "venue");
            case "Contact" -> Arrays.asList("firstName", "lastName", "email", "status", "createdDate");
            case "Account" -> Arrays.asList("accountName", "industry", "status", "revenue");
            case "Product" -> Arrays.asList("productName", "category", "price", "status");
            case "Venue" -> Arrays.asList("venueName", "city", "country", "capacity");
            case "Manufacturer" -> Arrays.asList("manufacturerName", "country", "industry", "status");
            default -> Arrays.asList("id", "name", "status", "createdDate");
        };
    }
    
    /**
     * Get field metadata for a specific field in an entity
     */
    @Cacheable(value = "fieldMetadata", key = "#entityType + '_' + #fieldName + '_' + #instanceId")
    public Optional<Object> getFieldMetadata(String entityType, String fieldName, String instanceId) {
        try {
            Optional<SettingsEntityDocument> settingsOpt =
                settingsEntityRepository.findByEntityAndInstanceId(entityType, instanceId);

            if (settingsOpt.isEmpty()) {
                List<SettingsEntityDocument> globalSettings =
                    settingsEntityRepository.findByEntity(entityType);
                if (!globalSettings.isEmpty()) {
                    settingsOpt = Optional.of(globalSettings.get(0));
                }
            }

            if (settingsOpt.isPresent()) {
                SettingsEntityDocument settings = settingsOpt.get();

                // Search in EntityList fields (contains the exportable fields)
                if (settings.getList() != null && settings.getList().getFields() != null) {
                    for (Object field : settings.getList().getFields()) {
                        try {
                            String currentFieldName = extractFieldProperty(field, "field");
                            if (fieldName.equals(currentFieldName)) {
                                return Optional.of(field);
                            }
                        } catch (Exception e) {
                            log.debug("Could not extract field name from field object: {}", field);
                        }
                    }
                }
            }

        } catch (Exception e) {
            log.error("Error retrieving field metadata for {} in entity {} instance {}: {}",
                fieldName, entityType, instanceId, e.getMessage(), e);
        }

        return Optional.empty();
    }

    /**
     * Build complete field metadata map from SettingsEntity
     * Returns Map<String, FieldMetadata> for validation and type information
     */
    @Cacheable(value = "fieldMetadataMap", key = "#entityType + '_' + #instanceId")
    public Map<String, FieldMetadata> buildFieldMetadataMap(String entityType, String instanceId) {
        log.debug("Building field metadata map for entity: {} in instance: {}", entityType, instanceId);

        Map<String, FieldMetadata> metadataMap = new HashMap<>();

        try {
            Optional<SettingsEntityDocument> settingsOpt =
                settingsEntityRepository.findByEntityAndInstanceId(entityType, instanceId);

            if (settingsOpt.isEmpty()) {
                List<SettingsEntityDocument> globalSettings =
                    settingsEntityRepository.findByEntity(entityType);
                if (!globalSettings.isEmpty()) {
                    settingsOpt = Optional.of(globalSettings.get(0));
                }
            }

            if (settingsOpt.isPresent()) {
                SettingsEntityDocument settings = settingsOpt.get();

                // Extract field metadata from EntityList
                if (settings.getList() != null && settings.getList().getFields() != null) {
                    for (Object field : settings.getList().getFields()) {
                        try {
                            FieldMetadata metadata = parseFieldMetadata(field);
                            if (metadata != null && metadata.getName() != null) {
                                metadataMap.put(metadata.getName(), metadata);

                                // Add nested field paths for array/object fields
                                addNestedFieldPaths(metadata, "", metadataMap);
                            }
                        } catch (Exception e) {
                            log.warn("Failed to parse field metadata for field: {}", field, e);
                        }
                    }
                }

                log.info("Built field metadata map for entity {} with {} fields: {}",
                    entityType, metadataMap.size(), metadataMap.keySet());

            } else {
                log.warn("No settings found for entity {}, creating fallback metadata map", entityType);
                metadataMap = createFallbackMetadataMap(entityType);
            }

        } catch (Exception e) {
            log.error("Error building field metadata map for entity {} in instance {}: {}",
                entityType, instanceId, e.getMessage(), e);
            metadataMap = createFallbackMetadataMap(entityType);
        }

        return metadataMap;
    }

    /**
     * Parse field metadata from SettingsEntity field object
     */
    private FieldMetadata parseFieldMetadata(Object fieldObject) {
        if (fieldObject == null) {
            return null;
        }

        try {
            // Use reflection to extract field properties
            String fieldName = extractFieldProperty(fieldObject, "field");
            String fieldType = extractFieldProperty(fieldObject, "type");

            if (fieldName == null || fieldName.trim().isEmpty()) {
                return null;
            }

            // Default type if not specified
            if (fieldType == null || fieldType.trim().isEmpty()) {
                fieldType = "String";
            }

            FieldMetadata metadata = new FieldMetadata();
            metadata.setName(fieldName);
            metadata.setType(fieldType);

            // Try to extract children for array/object fields
            List<FieldMetadata> children = extractChildrenMetadata(fieldObject);
            metadata.setChildren(children);

            // Extract additional properties
            Map<String, Object> properties = extractAdditionalProperties(fieldObject);
            metadata.setProperties(properties);

            return metadata;

        } catch (Exception e) {
            log.warn("Failed to parse field metadata from object: {}", fieldObject, e);
            return null;
        }
    }

    /**
     * Extract field property using reflection
     */
    private String extractFieldProperty(Object fieldObject, String propertyName) {
        try {
            // Try getter method first
            String getterName = "get" + propertyName.substring(0, 1).toUpperCase() + propertyName.substring(1);
            java.lang.reflect.Method getter = fieldObject.getClass().getMethod(getterName);
            Object value = getter.invoke(fieldObject);
            return value != null ? value.toString() : null;
        } catch (Exception e) {
            // Try direct field access
            try {
                java.lang.reflect.Field field = fieldObject.getClass().getDeclaredField(propertyName);
                field.setAccessible(true);
                Object value = field.get(fieldObject);
                return value != null ? value.toString() : null;
            } catch (Exception ex) {
                log.debug("Could not extract property '{}' from field object", propertyName);
                return null;
            }
        }
    }

    /**
     * Extract children metadata for array/object fields
     */
    private List<FieldMetadata> extractChildrenMetadata(Object fieldObject) {
        List<FieldMetadata> children = new ArrayList<>();

        try {
            // Try to get children property
            Object childrenObj = extractFieldPropertyAsObject(fieldObject, "children");
            if (childrenObj instanceof List<?> childrenList) {
                for (Object childObj : childrenList) {
                    FieldMetadata childMetadata = parseFieldMetadata(childObj);
                    if (childMetadata != null) {
                        children.add(childMetadata);
                    }
                }
            }
        } catch (Exception e) {
            log.debug("No children found for field object: {}", fieldObject);
        }

        return children.isEmpty() ? null : children;
    }

    /**
     * Extract field property as object using reflection
     */
    private Object extractFieldPropertyAsObject(Object fieldObject, String propertyName) {
        try {
            // Try getter method first
            String getterName = "get" + propertyName.substring(0, 1).toUpperCase() + propertyName.substring(1);
            java.lang.reflect.Method getter = fieldObject.getClass().getMethod(getterName);
            return getter.invoke(fieldObject);
        } catch (Exception e) {
            // Try direct field access
            try {
                java.lang.reflect.Field field = fieldObject.getClass().getDeclaredField(propertyName);
                field.setAccessible(true);
                return field.get(fieldObject);
            } catch (Exception ex) {
                return null;
            }
        }
    }

    /**
     * Extract additional properties from field object
     */
    private Map<String, Object> extractAdditionalProperties(Object fieldObject) {
        Map<String, Object> properties = new HashMap<>();

        try {
            // Try to extract common properties
            String[] commonProperties = {"format", "required", "defaultValue", "validation"};

            for (String prop : commonProperties) {
                Object value = extractFieldPropertyAsObject(fieldObject, prop);
                if (value != null) {
                    properties.put(prop, value);
                }
            }
        } catch (Exception e) {
            log.debug("Could not extract additional properties from field object");
        }

        return properties.isEmpty() ? null : properties;
    }

    /**
     * Add nested field paths to metadata map for array/object fields
     */
    private void addNestedFieldPaths(FieldMetadata metadata, String parentPath, Map<String, FieldMetadata> metadataMap) {
        if (metadata.getChildren() == null) {
            return;
        }

        String currentPath = parentPath.isEmpty() ? metadata.getName() : parentPath + "." + metadata.getName();

        for (FieldMetadata child : metadata.getChildren()) {
            String childPath = currentPath + "." + child.getName();
            metadataMap.put(childPath, child);

            // Recursively add nested paths
            if (child.getChildren() != null) {
                addNestedFieldPaths(child, currentPath, metadataMap);
            }
        }
    }

    /**
     * Create fallback metadata map when SettingsEntity is not available
     */
    private Map<String, FieldMetadata> createFallbackMetadataMap(String entityType) {
        Map<String, FieldMetadata> fallbackMap = new HashMap<>();

        List<String> defaultFields = getFallbackDefaultFields(entityType);

        for (String fieldName : defaultFields) {
            FieldMetadata metadata = FieldMetadata.createPrimitive(fieldName, "String");
            fallbackMap.put(fieldName, metadata);
        }

        log.info("Created fallback metadata map for entity {} with {} fields", entityType, fallbackMap.size());
        return fallbackMap;
    }

    /**
     * Validate fields against available metadata
     */
    public List<String> validateFields(List<String> requestedFields, String entityType, String instanceId) {
        if (requestedFields == null || requestedFields.isEmpty()) {
            return new ArrayList<>();
        }

        Map<String, FieldMetadata> metadataMap = buildFieldMetadataMap(entityType, instanceId);
        List<String> validFields = new ArrayList<>();
        List<String> invalidFields = new ArrayList<>();

        for (String field : requestedFields) {
            if (metadataMap.containsKey(field)) {
                validFields.add(field);
            } else {
                invalidFields.add(field);
            }
        }

        if (!invalidFields.isEmpty()) {
            log.warn("Invalid fields found for entity {}: {}. Available fields: {}",
                entityType, invalidFields, metadataMap.keySet());
        }

        log.info("Validated {} out of {} requested fields for entity {}",
            validFields.size(), requestedFields.size(), entityType);

        return validFields;
    }

    /**
     * Get array fields from requested fields list
     */
    public List<String> getArrayFields(List<String> requestedFields, String entityType, String instanceId) {
        if (requestedFields == null || requestedFields.isEmpty()) {
            return new ArrayList<>();
        }

        Map<String, FieldMetadata> metadataMap = buildFieldMetadataMap(entityType, instanceId);
        List<String> arrayFields = new ArrayList<>();

        for (String field : requestedFields) {
            FieldMetadata metadata = metadataMap.get(field);
            if (metadata != null && metadata.isArrayType()) {
                arrayFields.add(field);
            }
        }

        log.debug("Found {} array fields out of {} requested fields for entity {}: {}",
            arrayFields.size(), requestedFields.size(), entityType, arrayFields);

        return arrayFields;
    }
}
