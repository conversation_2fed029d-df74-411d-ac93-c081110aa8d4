package it.circle.plugin.service.impl;

import it.circle.plugin.service.ExcelExportService;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Slf4j
@Service
public class ExcelExportServiceImpl implements ExcelExportService {

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    public ExcelExportServiceImpl() {
        log.info("ExcelExportServiceImpl initialized");
    }

    @Override
    public ByteArrayOutputStream generateExcelFile(List<Map<String, Object>> data, String sheetName, String fileName) {
        log.info("Generating Excel file: {} with sheet: {} for {} records", fileName, sheetName, data.size());
        
        if (data.isEmpty()) {
            log.warn("No data provided for Excel generation");
            return new ByteArrayOutputStream();
        }
        
        // Extract headers from the first record
        List<String> headers = new ArrayList<>(data.get(0).keySet());
        
        return generateExcelFileWithHeaders(data, headers, sheetName, fileName);
    }

    @Override
    public ByteArrayOutputStream generateExcelFileWithHeaders(List<Map<String, Object>> data, List<String> headers, 
                                                            String sheetName, String fileName) {
        log.info("Generating Excel file: {} with custom headers: {} for {} records", fileName, headers, data.size());
        
        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet(sheetName);
            
            // Create header style
            CellStyle headerStyle = createHeaderStyle(workbook);
            
            // Create data style
            CellStyle dataStyle = createDataStyle(workbook);
            
            // Create header row
            createHeaderRow(sheet, headers, headerStyle);
            
            // Create data rows
            createDataRows(sheet, data, headers, dataStyle);
            
            // Auto-size columns
            autoSizeColumns(sheet, headers.size());
            
            // Write to ByteArrayOutputStream
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            workbook.write(outputStream);
            
            log.info("Successfully generated Excel file: {} with {} rows", fileName, data.size());
            return outputStream;
            
        } catch (IOException e) {
            log.error("Error generating Excel file: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to generate Excel file", e);
        }
    }

    @Override
    public String saveExcelFile(List<Map<String, Object>> data, String sheetName, String fileName, String filePath) {
        log.info("Saving Excel file to path: {}/{}.xlsx", filePath, fileName);
        
        ByteArrayOutputStream outputStream = generateExcelFile(data, sheetName, fileName);
        
        String fullPath = filePath + "/" + fileName + ".xlsx";
        
        try (FileOutputStream fileOut = new FileOutputStream(fullPath)) {
            outputStream.writeTo(fileOut);
            log.info("Successfully saved Excel file to: {}", fullPath);
            return fullPath;
        } catch (IOException e) {
            log.error("Error saving Excel file to: {}", fullPath, e);
            throw new RuntimeException("Failed to save Excel file", e);
        }
    }

    /**
     * Create header style with bold font and background color
     */
    private CellStyle createHeaderStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        
        // Set background color
        style.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        
        // Set borders
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        
        // Set font
        Font font = workbook.createFont();
        font.setBold(true);
        font.setFontHeightInPoints((short) 12);
        style.setFont(font);
        
        // Set alignment
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        
        return style;
    }

    /**
     * Create data style with borders
     */
    private CellStyle createDataStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        
        // Set borders
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        
        // Set alignment
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        
        return style;
    }

    /**
     * Create header row
     */
    private void createHeaderRow(Sheet sheet, List<String> headers, CellStyle headerStyle) {
        Row headerRow = sheet.createRow(0);
        
        for (int i = 0; i < headers.size(); i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(formatHeaderName(headers.get(i)));
            cell.setCellStyle(headerStyle);
        }
    }

    /**
     * Create data rows
     */
    private void createDataRows(Sheet sheet, List<Map<String, Object>> data, List<String> headers, CellStyle dataStyle) {
        for (int rowIndex = 0; rowIndex < data.size(); rowIndex++) {
            Row row = sheet.createRow(rowIndex + 1); // +1 because row 0 is header
            Map<String, Object> record = data.get(rowIndex);
            
            for (int colIndex = 0; colIndex < headers.size(); colIndex++) {
                Cell cell = row.createCell(colIndex);
                String header = headers.get(colIndex);
                Object value = record.get(header);
                
                setCellValue(cell, value);
                cell.setCellStyle(dataStyle);
            }
        }
    }

    /**
     * Set cell value based on the object type
     */
    private void setCellValue(Cell cell, Object value) {
        if (value == null) {
            cell.setCellValue("");
        } else if (value instanceof String) {
            cell.setCellValue((String) value);
        } else if (value instanceof Number) {
            cell.setCellValue(((Number) value).doubleValue());
        } else if (value instanceof Boolean) {
            cell.setCellValue((Boolean) value);
        } else if (value instanceof LocalDateTime) {
            cell.setCellValue(((LocalDateTime) value).format(DATE_FORMATTER));
        } else if (value instanceof Date) {
            cell.setCellValue((Date) value);
        } else {
            // For any other type, convert to string
            cell.setCellValue(value.toString());
        }
    }

    /**
     * Auto-size columns for better readability
     */
    private void autoSizeColumns(Sheet sheet, int columnCount) {
        for (int i = 0; i < columnCount; i++) {
            sheet.autoSizeColumn(i);
            
            // Set minimum width to avoid too narrow columns
            int currentWidth = sheet.getColumnWidth(i);
            if (currentWidth < 2000) { // Minimum width
                sheet.setColumnWidth(i, 2000);
            }
            
            // Set maximum width to avoid too wide columns
            if (currentWidth > 15000) { // Maximum width
                sheet.setColumnWidth(i, 15000);
            }
        }
    }

    /**
     * Format header names to be more readable
     */
    private String formatHeaderName(String header) {
        if (header == null || header.isEmpty()) {
            return header;
        }
        
        // Convert camelCase to Title Case
        String formatted = header.replaceAll("([a-z])([A-Z])", "$1 $2");
        
        // Capitalize first letter of each word
        String[] words = formatted.split(" ");
        StringBuilder result = new StringBuilder();
        
        for (String word : words) {
            if (!word.isEmpty()) {
                result.append(Character.toUpperCase(word.charAt(0)))
                      .append(word.substring(1).toLowerCase())
                      .append(" ");
            }
        }
        
        return result.toString().trim();
    }
}
