package it.circle.plugin.service.impl;



import java.io.ByteArrayOutputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import it.circle.crm.data.contact.document.ContactDocument;
import it.circle.plugin.model.Filter;
import it.circle.plugin.model.Target;
import it.circle.plugin.repository.ContactDocumentRepository;
import it.circle.plugin.service.ContactExporterService;
import it.circle.plugin.service.ExcelExportService;
import it.circle.plugin.utility.ExportUtility;
import it.circle.plugin.utility.QueryBuilderUtility;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
public class ContactExporterServiceImpl implements ContactExporterService {

    private final ContactDocumentRepository contactRepository;
    private final ExcelExportService excelExportService;
    private final MongoTemplate mongoTemplate;

    @Override
    public void processContactExport(String instanceId, String owner, Target target) {
//        log.info("Processing Contact export for instance: {}, owner: {}", instanceId, owner);
//
//        // Safe logging for filters count
//        int filtersCount = (target != null && target.getFilters() != null) ? target.getFilters().size() : 0;
//        log.info("Target fields: {}, filters count: {}", target.getFields(), filtersCount);
//
//        try {
//            // Step 1: Build query from filters
//            Query query = new Query();
//
//            // Add instance filter if provided
//            if (instanceId != null && !instanceId.isEmpty()) {
//                query.addCriteria(Criteria.where("instanceId").is(instanceId));
//                log.info("Added instanceId filter: {}", instanceId);
//            }
//
//            // Process target filters
//            if (target != null && target.getFilters() != null && !target.getFilters().isEmpty()) {
//                log.info("Processing {} filters", target.getFilters().size());
//                query = QueryBuilderUtility.buildQuery(target.getFilters(), instanceId);
//
//            } else {
//                log.info("No filters provided, will export all contacts for instance and owner");
//            }
//
//            log.info("Final MongoDB query: {}", query);
//
//            // Step 2: Fetch contact data from database
//            List<ContactDocument> contacts = mongoTemplate.find(query, ContactDocument.class);
//            log.info("Found {} contacts matching criteria", contacts.size());
//
//            if (contacts.isEmpty()) {
//                log.warn("No contacts found for the given criteria!");
//                // Log export summary with 0 records
//                ExportUtility.logExportSummary("Contact", instanceId, owner, 0, "No contacts found");
//                return;
//            }
//
//            // Step 3: Convert to export format with only requested fields
//            List<String> requestedFields = target.getFields();
//            if (requestedFields == null || requestedFields.isEmpty()) {
//                log.info("No specific fields requested, will use default fields for Contact entity");
//            } else {
//                log.info("Exporting {} specific fields: {}", requestedFields.size(), requestedFields);
//            }
//
//            // Use dynamic field extraction with filter context
//            List<Filter> appliedFilters = (target != null && target.getFilters() != null) ? target.getFilters() : new ArrayList<>();
//            List<Map<String, Object>> exportData = ExportUtility.convertEntityToExportData(contacts, requestedFields, "Contact", instanceId);
//
//            // Step 4: Generate Excel file using utility for filename
//            String fileName = ExportUtility.generateFileName("Contact", instanceId, owner);
//            ByteArrayOutputStream excelFile = excelExportService.generateExcelFile(
//                exportData, "Contacts", fileName
//            );
//
//            // Step 5: Save Excel file using utility (save in project's exports folder)
//            String projectDir = System.getProperty("user.dir");
//            String exportPath = projectDir + "/exports";
//            String savedPath = ExportUtility.saveExcelFile(excelFile, fileName, exportPath);
//            log.info("Excel file saved in project directory: {}", savedPath);
//
//            // Step 6: Log export summary
//            ExportUtility.logExportSummary("Contact", instanceId, owner, exportData.size(), fileName);
//
//
//            log.info("Successfully completed Contact export for {} records", exportData.size());
//
//        } catch (Exception e) {
//            log.error("Error processing Contact export for instance: {}", instanceId, e);
//            throw new RuntimeException("Failed to process Contact export", e);
//        }
    }
}
