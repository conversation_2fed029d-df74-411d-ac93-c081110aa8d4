package it.circle.plugin.service.impl;

import it.circle.crm.data.venue.document.VenueDocument;
import it.circle.plugin.model.Filter;
import it.circle.plugin.model.Target;
import it.circle.plugin.repository.VenueDocumentRepository;
import it.circle.plugin.service.VenueExporterService;
import it.circle.plugin.service.ExcelExportService;
import it.circle.plugin.utility.QueryBuilderUtility;
import it.circle.plugin.utility.ExportUtility;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class VenueExporterServiceImpl implements VenueExporterService {

    private final VenueDocumentRepository venueRepository;
    private final ExcelExportService excelExportService;
    private final MongoTemplate mongoTemplate;

    @Override
    public void processVenueExport(String instanceId, String owner, Target target) {
//        log.info("Processing Venue export for instance: {}, owner: {}", instanceId, owner);
//
//        // Safe logging for filters count
//        int filtersCount = (target != null && target.getFilters() != null) ? target.getFilters().size() : 0;
//        log.info("Target fields: {}, filters count: {}", target.getFields(), filtersCount);
//
//        try {
//            // Step 1: Build query from filters
//            Query query = new Query();
//
//            // Add instance filter if provided
//            if (instanceId != null && !instanceId.isEmpty()) {
//                query.addCriteria(Criteria.where("instance_id").is(instanceId));
//                log.info("Added instance_id filter: {}", instanceId);
//            }
//
//            // Add owner filter if provided
//            if (owner != null && !owner.isEmpty()) {
//                query.addCriteria(Criteria.where("owner").is(owner));
//                log.info("Added owner filter: {}", owner);
//            }
//
//            // Process target filters
//            if (target != null && target.getFilters() != null && !target.getFilters().isEmpty()) {
//                log.info("Processing {} filters", target.getFilters().size());
//                for (Filter filter : target.getFilters()) {
//                    log.info("Venue Filter - Field: {}, Operator: {}, Values: {}",
//                            filter.getField(), filter.getOperator(), filter.getValues());
//
//                    Criteria criteria = QueryBuilderUtility.buildCriteriaFromFilter(filter);
//                    if (criteria != null) {
//                        query.addCriteria(criteria);
//                        log.info("Added filter criteria for field: {}", filter.getField());
//                    }
//                }
//            } else {
//                log.info("No filters provided, will export all venues for instance and owner");
//            }
//
//            log.info("Final MongoDB query: {}", query);
//
//            // Step 2: Fetch venue data from database
//            List<VenueDocument> venues = mongoTemplate.find(query, VenueDocument.class);
//            log.info("Found {} venues matching criteria", venues.size());
//
//            if (venues.isEmpty()) {
//                log.warn("No venues found for the given criteria!");
//                // Log export summary with 0 records
//                ExportUtility.logExportSummary("Venue", instanceId, owner, 0, "No venues found");
//                return;
//            }
//
//            // Step 3: Convert to export format with only requested fields
//            List<String> requestedFields = target.getFields();
//            if (requestedFields == null || requestedFields.isEmpty()) {
//                log.info("No specific fields requested, will use default fields for Venue entity");
//            } else {
//                log.info("Exporting {} specific fields: {}", requestedFields.size(), requestedFields);
//            }
//
//            List<Map<String, Object>> exportData = ExportUtility.convertEntityToExportData(venues, requestedFields, "Venue", instanceId);
//
//            // Step 4: Generate Excel file using utility for filename
//            String fileName = ExportUtility.generateFileName("Venue", instanceId, owner);
//            ByteArrayOutputStream excelFile = excelExportService.generateExcelFile(
//                exportData, "Venues", fileName
//            );
//
//            // Step 5: Save Excel file using utility (save in project's exports folder)
//            String projectDir = System.getProperty("user.dir");
//            String exportPath = projectDir + "/exports";
//            String savedPath = ExportUtility.saveExcelFile(excelFile, fileName, exportPath);
//            log.info("Excel file saved in project directory: {}", savedPath);
//
//            // Step 6: Log export summary
//            ExportUtility.logExportSummary("Venue", instanceId, owner, exportData.size(), fileName);
//
//            // Step 7: Log what was actually exported
//            if (!exportData.isEmpty()) {
//                Set<String> actualFields = exportData.get(0).keySet();
//                log.info("Exported fields in Excel: {}", actualFields);
//            }
//
//            log.info("Successfully completed Venue export for {} records", exportData.size());
//
//        } catch (Exception e) {
//            log.error("Error processing Venue export for instance: {}", instanceId, e);
//            throw new RuntimeException("Failed to process Venue export", e);
//        }
    }
}
