package it.circle.plugin.service.impl;

import it.circle.crm.data.order.document.OrderDocument;
import it.circle.plugin.model.FieldMetadata;
import it.circle.plugin.model.FieldsLinee;
import it.circle.plugin.model.Filter;
import it.circle.plugin.model.Target;
import it.circle.plugin.repository.OrderDocumentRepository;
import it.circle.plugin.service.OrderExporterService;
import it.circle.plugin.service.ExcelExportService;
import it.circle.plugin.service.FieldValidationService;
import it.circle.plugin.service.FieldMetadataService;
import it.circle.plugin.utility.QueryBuilderUtility;
import it.circle.plugin.utility.ExportUtility;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.lang.reflect.Field;
import java.util.*;

@Slf4j
@Service
@RequiredArgsConstructor
public class OrderExporterServiceImpl implements OrderExporterService {

    private final OrderDocumentRepository orderRepository;
    private final ExcelExportService excelExportService;
    private final MongoTemplate mongoTemplate;
    private final FieldValidationService fieldValidationService;
    private final FieldMetadataService fieldMetadataService;

    @Override
    public void processOrderExport(String instanceId, String owner, Target target) {
        log.info("Processing Order export for instance: {}, owner: {}", instanceId, owner);

        // Safe logging for filters count
        int filtersCount = (target != null && target.getFilters() != null) ? target.getFilters().size() : 0;
        log.info("Target fields: {}, filters count: {}", target.getFields(), filtersCount);

        try {
            // Step 1: Handle filters - build query criteria
            List<Filter> filters = (target != null && target.getFilters() != null) ? target.getFilters() : null;

            Query query;
            if (filters == null || filters.isEmpty()) {
                log.info("No filters provided, fetching all orders for instance: {}", instanceId);
                query = new Query();
                if (instanceId != null && !instanceId.isEmpty()) {
                    query.addCriteria(Criteria.where("instanceId").is(instanceId));
                }
            } else {
                log.info("Building query with {} filters", filters.size());
                query = QueryBuilderUtility.buildQuery(filters, instanceId);
            }

            log.info("Final MongoDB query: {}", query);

            // Step 2: Fetch order data from database
            List<OrderDocument> orders = mongoTemplate.find(query, OrderDocument.class);

            // --- Field validation and filtering ---
            List<String> headerFields = target.getFields().getFieldsHeader();
            Map<String, FieldMetadata> orderMeta = fieldMetadataService.buildFieldMetadataMap("Order", instanceId);

            List<String> validHeaderFields = new ArrayList<>();
            for (String field : headerFields) {
                if (orderMeta.containsKey(field)) {
                    validHeaderFields.add(field);
                } else {
                    log.warn("Invalid field in fieldsHeader: {} — skipping", field);
                }
            }

            List<FieldsLinee> lineConfigs = target.getFields().getFieldsLinee();
            List<FieldsLinee> validLineConfigs = new ArrayList<>();

            for (FieldsLinee fieldsLinee : lineConfigs) {
                Map<String, FieldMetadata> subMeta = fieldMetadataService.buildFieldMetadataMap(fieldsLinee.getEntity(), instanceId);

                List<String> validLineFields = new ArrayList<>();
                for (String field : fieldsLinee.getFields()) {
                    if (subMeta.containsKey(field)) {
                        validLineFields.add(field);
                    } else {
                        log.warn("Invalid field in fieldsLinee for entity {}: {} — skipping", fieldsLinee.getEntity(), field);
                    }
                }

                if (!validLineFields.isEmpty()) {
                    validLineConfigs.add(new FieldsLinee(fieldsLinee.getEntity(), validLineFields));
                } else {
                    log.warn("No valid fields left for entity {} — skipping entire line config", fieldsLinee.getEntity());
                }
            }
            // --- End validation and filtering ---

            log.info("Found {} orders matching criteria", orders.size());

            if (orders.isEmpty()) {
                log.warn("No orders found for the given criteria!");
                ExportUtility.logExportSummary("Order", instanceId, owner, 0, "No orders found");
                return;
            }

            // Step 3: Build field metadata map from SettingsEntity
            Map<String, FieldMetadata> fieldMetadataMap = fieldMetadataService.buildFieldMetadataMap("Order", instanceId);
            log.info("Built field metadata map with {} fields for Order entity", fieldMetadataMap.size());

            List<Map<String, Object>> exportData = new ArrayList<>();

            for (OrderDocument order : orders) {
                // Header row
                Map<String, Object> headerMap = extractFields(order, validHeaderFields);

                // For each line config (e.g. OrderRow)
                for (FieldsLinee fieldsLinee : validLineConfigs) {
                    List<?> subRows = (List<?>) getFieldValue(order, fieldsLinee.getEntity());
                    if (subRows != null) {
                        for (Object row : subRows) {
                            Map<String, Object> lineMap = extractFields(row, fieldsLinee.getFields());

                            // Combine header + line
                            Map<String, Object> combined = new LinkedHashMap<>(headerMap);
                            combined.putAll(lineMap);
                            exportData.add(combined);
                        }
                    }
                }
            }

            String fileName = ExportUtility.generateFileName("Order", instanceId, owner);
            ByteArrayOutputStream excelFile = excelExportService.generateExcelFile(
                    exportData, "Orders", fileName
            );

            String projectDir = System.getProperty("user.dir");
            String exportPath = projectDir + "/exports";
            String savedPath = ExportUtility.saveExcelFile(excelFile, fileName, exportPath);
            log.info("Excel file saved in project directory: {}", savedPath);

            if (!exportData.isEmpty()) {
                Set<String> actualFields = exportData.get(0).keySet();
                log.info("Exported fields in Excel: {}", actualFields);
            }

            log.info("Successfully completed Order export for {} records with array expansion support", exportData.size());

        } catch (Exception e) {
            log.error("Error processing Order export for instance: {}", instanceId, e);
            throw new RuntimeException("Failed to process Order export", e);
        }
    }



    private Map<String, Object> extractFields(Object obj, List<String> fields) {
        Map<String, Object> map = new LinkedHashMap<>();
        for (String fieldName : fields) {
            Object value = getFieldValue(obj, fieldName);
            map.put(fieldName, value != null ? value : "");
        }
        return map;
    }

    private Object getFieldValue(Object obj, String fieldName) {
        try {
            Field field = obj.getClass().getDeclaredField(fieldName);
            field.setAccessible(true);
            return field.get(obj);
        } catch (NoSuchFieldException e) {
            return null; // or throw if strict
        } catch (IllegalAccessException e) {
            throw new RuntimeException(e);
        }
    }




}
