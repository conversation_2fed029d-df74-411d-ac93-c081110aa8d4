package it.circle.plugin.service.impl;

import it.circle.crm.data.order.document.OrderDocument;
import it.circle.plugin.model.FieldMetadata;
import it.circle.plugin.model.FieldsLinee;
import it.circle.plugin.model.Filter;
import it.circle.plugin.model.Target;
import it.circle.plugin.repository.OrderDocumentRepository;
import it.circle.plugin.service.OrderExporterService;
import it.circle.plugin.service.ExcelExportService;
import it.circle.plugin.service.FieldValidationService;
import it.circle.plugin.service.FieldMetadataService;
import it.circle.plugin.utility.QueryBuilderUtility;
import it.circle.plugin.utility.ExportUtility;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.lang.reflect.Field;
import java.util.*;

@Slf4j
@Service
@RequiredArgsConstructor
public class OrderExporterServiceImpl implements OrderExporterService {

    private final OrderDocumentRepository orderRepository;
    private final ExcelExportService excelExportService;
    private final MongoTemplate mongoTemplate;
    private final FieldValidationService fieldValidationService;
    private final FieldMetadataService fieldMetadataService;

    @Override
    public void processOrderExport(String instanceId, String owner, Target target) {
        log.info("Processing Order export for instance: {}, owner: {}", instanceId, owner);

        // Validate input parameters
        if (target == null) {
            throw new IllegalArgumentException("Target cannot be null");
        }
        if (target.getFields() == null) {
            throw new IllegalArgumentException("Target fields cannot be null");
        }
        if (target.getFields().getFieldsHeader() == null || target.getFields().getFieldsHeader().isEmpty()) {
            throw new IllegalArgumentException("Target fieldsHeader cannot be null or empty");
        }

        // Safe logging for filters count
        int filtersCount = (target.getFilters() != null) ? target.getFilters().size() : 0;
        log.info("Target fields header: {}, line configs: {}, filters count: {}",
                target.getFields().getFieldsHeader(),
                target.getFields().getFieldsLinee() != null ? target.getFields().getFieldsLinee().size() : 0,
                filtersCount);

        // Log detailed field information
        if (target.getFields().getFieldsLinee() != null) {
            for (FieldsLinee fieldsLinee : target.getFields().getFieldsLinee()) {
                log.info("Line config - Entity: {}, Fields: {}", fieldsLinee.getEntity(), fieldsLinee.getFields());
            }
        }

        try {
            // Step 1: Handle filters - build query criteria
            List<Filter> filters = target.getFilters();

            Query query;
            if (filters == null || filters.isEmpty()) {
                log.info("No filters provided, fetching all orders for instance: {}", instanceId);
                query = new Query();
                if (instanceId != null && !instanceId.isEmpty()) {
                    query.addCriteria(Criteria.where("instanceId").is(instanceId));
                }
            } else {
                log.info("Building query with {} filters", filters.size());
                query = QueryBuilderUtility.buildQuery(filters, instanceId);
            }

            log.info("Final MongoDB query: {}", query);

            // Step 2: Fetch order data from database
            List<OrderDocument> orders = mongoTemplate.find(query, OrderDocument.class);

            // --- Field validation and filtering ---
            List<String> headerFields = target.getFields().getFieldsHeader();
            Map<String, FieldMetadata> orderMeta = fieldMetadataService.buildFieldMetadataMap("Order", instanceId);

            List<String> validHeaderFields = new ArrayList<>();
            for (String field : headerFields) {
                if (orderMeta.containsKey(field)) {
                    validHeaderFields.add(field);
                } else if (isCommonMongoField(field)) {
                    // Allow common MongoDB fields that might not be in SettingsEntity
                    validHeaderFields.add(field);
                    log.info("Adding common MongoDB field: {}", field);
                } else {
                    log.warn("Invalid field in fieldsHeader: {} — skipping. Available fields: {}",
                            field, orderMeta.keySet());
                }
            }

            // Handle fieldsLinee - check for null
            List<FieldsLinee> lineConfigs = target.getFields().getFieldsLinee();
            List<FieldsLinee> validLineConfigs = new ArrayList<>();

            if (lineConfigs != null) {
                for (FieldsLinee fieldsLinee : lineConfigs) {
                    if (fieldsLinee == null || fieldsLinee.getEntity() == null || fieldsLinee.getFields() == null) {
                        log.warn("Skipping null or invalid fieldsLinee configuration");
                        continue;
                    }

                    Map<String, FieldMetadata> subMeta = fieldMetadataService.buildFieldMetadataMap(fieldsLinee.getEntity(), instanceId);

                    List<String> validLineFields = new ArrayList<>();
                    for (String field : fieldsLinee.getFields()) {
                        if (field != null && subMeta.containsKey(field)) {
                            validLineFields.add(field);
                        } else if (field != null && isCommonMongoField(field)) {
                            // Allow common MongoDB fields that might not be in SettingsEntity
                            validLineFields.add(field);
                            log.info("Adding common MongoDB field for entity {}: {}", fieldsLinee.getEntity(), field);
                        } else {
                            log.warn("Invalid field in fieldsLinee for entity {}: {} — skipping. Available fields: {}",
                                    fieldsLinee.getEntity(), field, subMeta.keySet());
                        }
                    }

                    if (!validLineFields.isEmpty()) {
                        validLineConfigs.add(new FieldsLinee(fieldsLinee.getEntity(), validLineFields));
                    } else {
                        log.warn("No valid fields left for entity {} — skipping entire line config", fieldsLinee.getEntity());
                    }
                }
            }
            // --- End validation and filtering ---

            log.info("Found {} orders matching criteria", orders.size());

            if (orders.isEmpty()) {
                log.warn("No orders found for the given criteria!");
                ExportUtility.logExportSummary("Order", instanceId, owner, 0, "No orders found");
                return;
            }

            // Validate that we have at least some valid fields
            if (validHeaderFields.isEmpty() && validLineConfigs.isEmpty()) {
                log.error("No valid fields found for export. Header fields: {}, Line configs: {}",
                         headerFields, lineConfigs);
                throw new RuntimeException("No valid fields found for export");
            }

            log.info("Valid header fields: {}", validHeaderFields);
            log.info("Valid line configs: {}", validLineConfigs.size());

            // Step 3: Build field metadata map from SettingsEntity
            Map<String, FieldMetadata> fieldMetadataMap = fieldMetadataService.buildFieldMetadataMap("Order", instanceId);
            log.info("Built field metadata map with {} fields for Order entity", fieldMetadataMap.size());

            List<Map<String, Object>> exportData = new ArrayList<>();

            for (OrderDocument order : orders) {
                try {
                    log.debug("Processing order: {}", getFieldValue(order, "_id"));

                    // Header row
                    Map<String, Object> headerMap = extractFields(order, validHeaderFields);
                    log.debug("Header map for order {}: {}", getFieldValue(order, "_id"), headerMap);

                    // If no line configs, just add header data
                    if (validLineConfigs.isEmpty()) {
                        log.debug("No line configs, adding header data only");
                        exportData.add(headerMap);
                        continue;
                    }

                    // For each line config (e.g. OrderRow)
                    boolean hasLineData = false;
                    for (FieldsLinee fieldsLinee : validLineConfigs) {
                        try {
                            log.debug("Processing line config for entity: {}", fieldsLinee.getEntity());
                            List<?> subRows = (List<?>) getFieldValue(order, fieldsLinee.getEntity());
                            log.debug("Found {} sub-rows for entity {}",
                                     subRows != null ? subRows.size() : 0, fieldsLinee.getEntity());

                            if (subRows != null && !subRows.isEmpty()) {
                                hasLineData = true;
                                for (Object row : subRows) {
                                    if (row != null) {
                                        Map<String, Object> lineMap = extractFields(row, fieldsLinee.getFields());
                                        log.debug("Line map: {}", lineMap);

                                        // Combine header + line
                                        Map<String, Object> combined = new LinkedHashMap<>(headerMap);
                                        combined.putAll(lineMap);
                                        exportData.add(combined);
                                        log.debug("Added combined row: {}", combined);
                                    }
                                }
                            } else {
                                log.debug("No sub-rows found for entity {} in order {}",
                                         fieldsLinee.getEntity(), getFieldValue(order, "_id"));
                            }
                        } catch (Exception e) {
                            log.error("Error processing line config for entity {} in order {}: {}",
                                     fieldsLinee.getEntity(), getFieldValue(order, "_id"), e.getMessage(), e);
                        }
                    }

                    // If no line data was found, still add the header row
                    if (!hasLineData) {
                        log.debug("No line data found, adding header row only");
                        exportData.add(headerMap);
                    }

                } catch (Exception e) {
                    log.error("Error processing order {}: {}", getFieldValue(order, "_id"), e.getMessage(), e);
                    // Continue with next order instead of failing completely
                }
            }

            log.info("Generated {} export data rows from {} orders", exportData.size(), orders.size());

            if (exportData.isEmpty()) {
                log.warn("No export data generated despite having {} orders", orders.size());
                ExportUtility.logExportSummary("Order", instanceId, owner, 0, "No export data generated");
                return;
            }

            String fileName = ExportUtility.generateFileName("Order", instanceId, owner);
            ByteArrayOutputStream excelFile = excelExportService.generateExcelFile(
                    exportData, "Orders", fileName
            );

            String projectDir = System.getProperty("user.dir");
            String exportPath = projectDir + "/exports";
            String savedPath = ExportUtility.saveExcelFile(excelFile, fileName, exportPath);
            log.info("Excel file saved in project directory: {}", savedPath);

            if (!exportData.isEmpty()) {
                Set<String> actualFields = exportData.get(0).keySet();
                log.info("Exported fields in Excel: {}", actualFields);
            }

            log.info("Successfully completed Order export for {} records with array expansion support", exportData.size());

        } catch (Exception e) {
            log.error("Error processing Order export for instance: {}", instanceId, e);
            throw new RuntimeException("Failed to process Order export", e);
        }
    }



    private Map<String, Object> extractFields(Object obj, List<String> fields) {
        Map<String, Object> map = new LinkedHashMap<>();
        if (obj == null || fields == null) {
            return map;
        }

        for (String fieldName : fields) {
            if (fieldName != null && !fieldName.trim().isEmpty()) {
                Object value = getFieldValue(obj, fieldName);
                map.put(fieldName, value != null ? value : "");
            }
        }
        return map;
    }

    private Object getFieldValue(Object obj, String fieldName) {
        if (obj == null || fieldName == null || fieldName.trim().isEmpty()) {
            return null;
        }

        try {
            // Handle nested field access (e.g., "address.street")
            if (fieldName.contains(".")) {
                return getNestedFieldValue(obj, fieldName);
            }

            // Try direct field access first
            Field field = obj.getClass().getDeclaredField(fieldName);
            field.setAccessible(true);
            return field.get(obj);

        } catch (NoSuchFieldException e) {
            // Try alternative field names or camelCase conversion
            String alternativeFieldName = convertToCamelCase(fieldName);
            if (!alternativeFieldName.equals(fieldName)) {
                try {
                    Field field = obj.getClass().getDeclaredField(alternativeFieldName);
                    field.setAccessible(true);
                    return field.get(obj);
                } catch (NoSuchFieldException | IllegalAccessException ex) {
                    // Continue to next attempt
                }
            }

            // Try snake_case to camelCase conversion
            String camelCaseField = convertSnakeToCamelCase(fieldName);
            if (!camelCaseField.equals(fieldName)) {
                try {
                    Field field = obj.getClass().getDeclaredField(camelCaseField);
                    field.setAccessible(true);
                    return field.get(obj);
                } catch (NoSuchFieldException | IllegalAccessException ex) {
                    // Continue to next attempt
                }
            }

            // Try common field mappings
            String mappedField = mapCommonField(fieldName);
            if (mappedField != null && !mappedField.equals(fieldName)) {
                try {
                    Field field = obj.getClass().getDeclaredField(mappedField);
                    field.setAccessible(true);
                    return field.get(obj);
                } catch (NoSuchFieldException | IllegalAccessException ex) {
                    // Continue to next attempt
                }
            }

            log.debug("Field '{}' not found in class {} after trying alternatives", fieldName, obj.getClass().getSimpleName());
            return null;

        } catch (IllegalAccessException e) {
            log.error("Cannot access field '{}' in class {}: {}", fieldName, obj.getClass().getSimpleName(), e.getMessage());
            return null;
        } catch (Exception e) {
            log.error("Unexpected error accessing field '{}' in class {}: {}", fieldName, obj.getClass().getSimpleName(), e.getMessage());
            return null;
        }
    }

    private Object getNestedFieldValue(Object obj, String fieldPath) {
        String[] parts = fieldPath.split("\\.");
        Object current = obj;

        for (String part : parts) {
            if (current == null) {
                return null;
            }
            current = getFieldValue(current, part);
        }

        return current;
    }

    private String convertToCamelCase(String fieldName) {
        if (fieldName == null || fieldName.isEmpty()) {
            return fieldName;
        }

        // Convert first character to lowercase
        return Character.toLowerCase(fieldName.charAt(0)) + fieldName.substring(1);
    }

    private String convertSnakeToCamelCase(String snakeCase) {
        if (snakeCase == null || !snakeCase.contains("_")) {
            return snakeCase;
        }

        StringBuilder camelCase = new StringBuilder();
        boolean capitalizeNext = false;

        for (char c : snakeCase.toCharArray()) {
            if (c == '_') {
                capitalizeNext = true;
            } else if (capitalizeNext) {
                camelCase.append(Character.toUpperCase(c));
                capitalizeNext = false;
            } else {
                camelCase.append(c);
            }
        }

        return camelCase.toString();
    }

    /**
     * Check if a field is a common MongoDB field that should be allowed even if not in SettingsEntity
     */
    private boolean isCommonMongoField(String fieldName) {
        if (fieldName == null) {
            return false;
        }

        // Common MongoDB and entity fields that might not be in SettingsEntity
        return fieldName.equals("_id") ||
               fieldName.equals("id") ||
               fieldName.equals("instanceId") ||
               fieldName.equals("company_id") ||
               fieldName.equals("companyId") ||
               fieldName.equals("created") ||
               fieldName.equals("updated") ||
               fieldName.equals("createdDate") ||
               fieldName.equals("updatedDate");
    }

    /**
     * Map common field name variations to actual field names
     */
    private String mapCommonField(String fieldName) {
        if (fieldName == null) {
            return null;
        }

        return switch (fieldName) {
            case "company_id" -> "companyId";
            case "_id" -> "id";
            case "instance_id" -> "instanceId";
            case "created_date" -> "created";
            case "updated_date" -> "updated";
            case "order_id" -> "orderId";
            case "customer_id" -> "customerId";
            case "venue_id" -> "venueId";
            default -> null;
        };
    }
}
