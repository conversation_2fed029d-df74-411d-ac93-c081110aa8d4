package it.circle.plugin.service;

import java.io.ByteArrayOutputStream;
import java.util.List;
import java.util.Map;

public interface ExcelExportService {
    
    /**
     * Generate Excel file from data
     * 
     * @param data List of maps containing the data to export
     * @param sheetName Name of the Excel sheet
     * @param fileName Name of the Excel file (without extension)
     * @return ByteArrayOutputStream containing the Excel file
     */
    ByteArrayOutputStream generateExcelFile(List<Map<String, Object>> data, String sheetName, String fileName);
    
    /**
     * Generate Excel file with custom headers
     * 
     * @param data List of maps containing the data to export
     * @param headers List of column headers in order
     * @param sheetName Name of the Excel sheet
     * @param fileName Name of the Excel file (without extension)
     * @return ByteArrayOutputStream containing the Excel file
     */
    ByteArrayOutputStream generateExcelFileWithHeaders(List<Map<String, Object>> data, List<String> headers, String sheetName, String fileName);
    
    /**
     * Save Excel file to specified path
     * 
     * @param data List of maps containing the data to export
     * @param sheetName Name of the Excel sheet
     * @param fileName Name of the Excel file (without extension)
     * @param filePath Path where to save the file
     * @return Full path of the saved file
     */
    String saveExcelFile(List<Map<String, Object>> data, String sheetName, String fileName, String filePath);
}
