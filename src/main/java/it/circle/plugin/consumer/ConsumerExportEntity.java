package it.circle.plugin.consumer;

import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.databind.ObjectMapper;

import it.circle.plugin.model.ExportMessage;
import it.circle.plugin.model.Target;
import it.circle.plugin.service.AccountExporterService;
import it.circle.plugin.service.ContactExporterService;
import it.circle.plugin.service.ManufacturerExporterService;
import it.circle.plugin.service.OrderExporterService;
import it.circle.plugin.service.ProductExporterService;
import it.circle.plugin.service.VenueExporterService;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
@RequiredArgsConstructor
@RabbitListener(queues="${queue.name.exporter}")
public class ConsumerExportEntity {

    private final ObjectMapper objectMapper;

    @Autowired
    private ContactExporterService contactExporterService;

    @Autowired
    private OrderExporterService orderExporterService;

    @Autowired
    private ProductExporterService productExporterService;

    @Autowired
    private AccountExporterService accountExporterService;

    @Autowired
    private VenueExporterService venueExporterService;

    @Autowired
    private ManufacturerExporterService manufacturerExporterService;


    @RabbitHandler
    @SneakyThrows
    public void readMessage(byte[] message) {
        String messageReceived = new String(message);
        log.info("Received export message from queue: {}", messageReceived);

        try {
            // Parse the JSON message into our model
            ExportMessage exportMessage = objectMapper.readValue(messageReceived, ExportMessage.class);

            log.info("Parsed export message - Instance ID: {}, Owner: {}, Targets count: {}",
                    exportMessage.getInstanceId(),
                    exportMessage.getOwner(),
                    exportMessage.getTargets().size());

            for (Target target : exportMessage.getTargets()) {
                processTarget(exportMessage.getInstanceId(), exportMessage.getOwner(), target);
            }

        } catch (Exception e) {
            log.error("Error processing export message: {}", e.getMessage(), e);
            throw e;
        }
    }

    private void processTarget(String instanceId, String owner, Target target) {
        // Convert entity name to proper case for SettingsEntity lookup
        String entityName = normalizeEntityName(target.getEntity());

        // Create a new target with normalized entity name
        Target normalizedTarget = new Target();
        normalizedTarget.setEntity(entityName);
        normalizedTarget.setFields(target.getFields());
        normalizedTarget.setFilters(target.getFilters());

        switch (target.getEntity().toLowerCase()) {
            case "contact":
                contactExporterService.processContactExport(instanceId, owner, normalizedTarget);
                break;
            case "order":
                orderExporterService.processOrderExport(instanceId, owner, normalizedTarget);
                break;
            case "account":
                accountExporterService.processAccountExport(instanceId, owner, normalizedTarget);
                break;
            case "product":
                productExporterService.processProductExport(instanceId, owner, normalizedTarget);
                break;
            case "venue":
                venueExporterService.processVenueExport(instanceId, owner, normalizedTarget);
                break;
            case "manufacturer":
                manufacturerExporterService.processManufacturerExport(instanceId, owner, normalizedTarget);
                break;
            default:
                log.warn("Unknown entity type: {}", target.getEntity());
        }
    }

    /**
     * Normalize entity name to proper case for SettingsEntity lookup
     */
    private String normalizeEntityName(String entityName) {
        if (entityName == null || entityName.isEmpty()) {
            return entityName;
        }

        // Convert to proper case (first letter uppercase, rest as-is)
        return entityName.substring(0, 1).toUpperCase() + entityName.substring(1).toLowerCase();
    }


}