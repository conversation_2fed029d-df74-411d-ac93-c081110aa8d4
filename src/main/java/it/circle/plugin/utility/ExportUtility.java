package it.circle.plugin.utility;


import it.circle.plugin.model.FieldMetadata;
import it.circle.plugin.service.FieldMetadataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.ByteArrayOutputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class ExportUtility {

    private static FieldMetadataService fieldMetadataService;

    @Autowired
    public void setFieldMetadataService(FieldMetadataService fieldMetadataService) {
        ExportUtility.fieldMetadataService = fieldMetadataService;
    }

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private static final DateTimeFormatter FILENAME_DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss");

    private ExportUtility() {
        // Utility class - prevent instantiation
    }

    /**
     * Generate filename for export with timestamp
     */
    public static String generateFileName(String entityType, String instanceId, String owner) {
        String timestamp = LocalDateTime.now().format(FILENAME_DATE_FORMATTER);
        String ownerPrefix = owner != null ? owner.split("@")[0] : "unknown";
        String entityLower = entityType.toLowerCase();
        return String.format("%s_export_%s_%s_%s", entityLower, instanceId, ownerPrefix, timestamp);
    }

    /**
     * Save Excel file to specified path
     */
    public static String saveExcelFile(ByteArrayOutputStream excelFile, String fileName, String exportPath) {
        try {
            String fullPath = exportPath + "/" + fileName + ".xlsx";

            // Create directory if it doesn't exist
            java.nio.file.Files.createDirectories(java.nio.file.Paths.get(exportPath));

            // Save file
            try (FileOutputStream fileOut = new FileOutputStream(fullPath)) {
                excelFile.writeTo(fileOut);
                log.info("Excel file saved to: {}", fullPath);
            }

            return fullPath;

        } catch (IOException e) {
            log.error("Error saving Excel file: {}", fileName, e);
            throw new RuntimeException("Failed to save Excel file", e);
        }
    }

    /**
     * Format LocalDateTime for export
     */
    public static String formatDateForExport(LocalDateTime dateTime) {
        return dateTime != null ? dateTime.format(DATE_FORMATTER) : null;
    }

    /**
     * Format Date for export (converts to LocalDateTime first)
     */
    public static String formatDateForExport(Date date) {
        if (date == null) {
            return null;
        }
        LocalDateTime localDateTime = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        return formatDateForExport(localDateTime);
    }

    /**
     * Format any date object for export (handles both Date and LocalDateTime)
     */
    public static String formatDateForExport(Object dateObject) {
        if (dateObject == null) {
            return null;
        } else if (dateObject instanceof LocalDateTime) {
            return formatDateForExport((LocalDateTime) dateObject);
        } else if (dateObject instanceof Date) {
            return formatDateForExport((Date) dateObject);
        } else {
            log.warn("Unknown date type: {}, converting to string", dateObject.getClass().getSimpleName());
            return dateObject.toString();
        }
    }

    /**
     * Get default field list if none provided - now uses dynamic field retrieval from SettingsEntity
     */
    public static List<String> getDefaultFields(String entityType, String instanceId) {
        if (fieldMetadataService != null) {
            try {
                List<String> dynamicFields = fieldMetadataService.getEntityFields(entityType, instanceId);
                if (!dynamicFields.isEmpty()) {
                    log.info("Retrieved {} dynamic fields for entity {}: {}",
                        dynamicFields.size(), entityType, dynamicFields);
                    return dynamicFields;
                }
            } catch (Exception e) {
                log.warn("Failed to retrieve dynamic fields for entity {}, falling back to static defaults: {}",
                    entityType, e.getMessage());
            }
        }

        // Fallback to static default fields
        return getFallbackDefaultFields(entityType);
    }

    /**
     * Get fallback default field list when dynamic retrieval fails
     */
    private static List<String> getFallbackDefaultFields(String entityType) {
        return switch (entityType) {
            case "Order" -> Arrays.asList("customer", "order_id", "date", "status", "total_paid", "payment_method", "venue");
            case "Contact" -> Arrays.asList("firstName", "lastName", "email", "status", "createdDate");
            case "Account" -> Arrays.asList("accountName", "industry", "status", "revenue");
            case "Product" -> Arrays.asList("productName", "category", "price", "status");
            case "Venue" -> Arrays.asList("venueName", "city", "country", "capacity");
            case "Manufacturer" -> Arrays.asList("manufacturerName", "country", "industry", "status");
            default -> Arrays.asList("id", "name", "status", "createdDate");
        };
    }

    /**
     * Log export summary
     */
    public static void logExportSummary(String entityType, String instanceId, String owner,
                                      int recordCount, String fileName) {
        log.info("Export Summary - Entity: {}, Instance: {}, Owner: {}, Records: {}, File: {}",
                entityType, instanceId, owner, recordCount, fileName);
    }


       /**
     * Convert entity list to export data format - Generic method for all entities
     */
    public static List<Map<String, Object>> convertEntityToExportData(List<?> entities, List<String> requestedFields, String entityType, String instanceId) {
        return entities.stream()
                .map(entity -> convertEntityToMap(entity, requestedFields, entityType, instanceId))
                .collect(Collectors.toList());
    }

    /**
     * Convert entities to export data with array expansion support
     * When array fields are present, creates multiple rows per entity
     */
    public static List<Map<String, Object>> convertEntityToExportDataWithArrayExpansion(
            List<?> entities,
            List<String> requestedFields,
            String entityType,
            String instanceId,
            Map<String, FieldMetadata> fieldMetadataMap) {

        log.info("Converting {} entities to export data with array expansion for entity type: {}",
            entities.size(), entityType);

        if (entities.isEmpty()) {
            return new ArrayList<>();
        }

        // If no specific fields requested, use default fields
        if (requestedFields == null || requestedFields.isEmpty()) {
            requestedFields = ExportUtility.getDefaultFields(entityType, instanceId);
        }

        // Identify array fields
        List<String> arrayFields = identifyArrayFields(requestedFields, fieldMetadataMap);

        List<Map<String, Object>> exportData = new ArrayList<>();

        for (Object entity : entities) {
            if (arrayFields.isEmpty()) {
                // No arrays - use existing logic
                exportData.add(convertEntityToMap(entity, requestedFields, entityType, instanceId));
            } else {
                // Arrays present - expand rows
                List<Map<String, Object>> expandedRows = expandEntityRows(
                    entity, requestedFields, arrayFields, fieldMetadataMap, entityType, instanceId);
                exportData.addAll(expandedRows);
            }
        }

        log.info("Generated {} export rows from {} entities (with array expansion)",
            exportData.size(), entities.size());

        return exportData;
    }

    /**
     * Identify array fields from requested fields using metadata map
     */
    private static List<String> identifyArrayFields(List<String> requestedFields, Map<String, FieldMetadata> fieldMetadataMap) {
        if (requestedFields == null || fieldMetadataMap == null) {
            return new ArrayList<>();
        }

        List<String> arrayFields = new ArrayList<>();

        for (String field : requestedFields) {
            FieldMetadata metadata = fieldMetadataMap.get(field);
            if (metadata != null && metadata.isArrayType()) {
                arrayFields.add(field);
            }

            // Check for nested array fields (e.g., "OrderRow.product_name" where OrderRow is array)
            if (field.contains(".")) {
                String parentField = field.substring(0, field.indexOf("."));
                FieldMetadata parentMetadata = fieldMetadataMap.get(parentField);
                if (parentMetadata != null && parentMetadata.isArrayType()) {
                    if (!arrayFields.contains(parentField)) {
                        arrayFields.add(parentField);
                    }
                }
            }
        }

        log.debug("Identified {} array fields: {}", arrayFields.size(), arrayFields);
        return arrayFields;
    }

    /**
     * Expand entity rows for array fields
     * Creates multiple rows when array fields are present
     */
    private static List<Map<String, Object>> expandEntityRows(
            Object entity,
            List<String> requestedFields,
            List<String> arrayFields,
            Map<String, FieldMetadata> fieldMetadataMap,
            String entityType,
            String instanceId) {

        List<Map<String, Object>> expandedRows = new ArrayList<>();

        try {
            // Get the primary array field (first one found)
            String primaryArrayField = arrayFields.get(0);

            // Extract array values from entity
            Object arrayValue = getEntityFieldValue(entity, primaryArrayField, entityType);

            if (arrayValue instanceof Collection<?> collection && !collection.isEmpty()) {
                // Create one row for each array element
                int index = 0;
                for (Object arrayElement : collection) {
                    Map<String, Object> row = new LinkedHashMap<>();

                    // Add all requested fields to the row
                    for (String field : requestedFields) {
                        Object value = getFieldValueForArrayExpansion(
                            entity, field, arrayElement, primaryArrayField, entityType, index);
                        row.put(field, value);
                    }

                    expandedRows.add(row);
                    index++;
                }
            } else {
                // Array is empty or null, create single row with null array values
                Map<String, Object> row = new LinkedHashMap<>();

                for (String field : requestedFields) {
                    Object value = getFieldValueForArrayExpansion(
                        entity, field, null, primaryArrayField, entityType, 0);
                    row.put(field, value);
                }

                expandedRows.add(row);
            }

        } catch (Exception e) {
            log.error("Error expanding entity rows for entity type {}: {}", entityType, e.getMessage(), e);

            // Fallback: create single row without array expansion
            Map<String, Object> fallbackRow = convertEntityToMap(entity, requestedFields, entityType, instanceId);
            expandedRows.add(fallbackRow);
        }

        return expandedRows;
    }

    /**
     * Get field value for array expansion
     * Handles both array fields and non-array fields appropriately
     */
    private static Object getFieldValueForArrayExpansion(
            Object entity,
            String fieldName,
            Object arrayElement,
            String primaryArrayField,
            String entityType,
            int arrayIndex) {

        try {
            // Check if this field is related to the array
            if (fieldName.startsWith(primaryArrayField + ".")) {
                // This is a nested field within the array (e.g., "OrderRow.product_name")
                if (arrayElement != null) {
                    String nestedFieldName = fieldName.substring(primaryArrayField.length() + 1);
                    return extractFieldValueUsingReflection(arrayElement, nestedFieldName);
                } else {
                    return null;
                }
            } else if (fieldName.equals(primaryArrayField)) {
                // This is the array field itself - return the current element
                return formatFieldValue(arrayElement);
            } else {
                // This is a regular field from the main entity - replicate across all rows
                return getEntityFieldValue(entity, fieldName, entityType);
            }

        } catch (Exception e) {
            log.warn("Error getting field value for array expansion - field: {}, entity: {}: {}",
                fieldName, entityType, e.getMessage());
            return null;
        }
    }

    /**
     * Convert single entity to Map with only requested fields - Generic method for all entities
     */
    public static Map<String, Object> convertEntityToMap(Object entity, List<String> requestedFields, String entityType, String instanceId) {
        Map<String, Object> entityMap = new LinkedHashMap<>();

        // If no specific fields requested, use default fields from utility (now dynamic)
        if (requestedFields == null || requestedFields.isEmpty()) {
            requestedFields = ExportUtility.getDefaultFields(entityType, instanceId);
        }

        for (String field : requestedFields) {
            // Normalize field name to handle variations
            String normalizedField = FieldMappingUtility.normalizeFieldName(field);
            Object value = getEntityFieldValue(entity, normalizedField, entityType);
            entityMap.put(field, value); // Use original field name as key for consistency
        }

        return entityMap;
    }

    /**
     * Get field value from any entity using reflection - Generic method for all entities
     */
    public static Object getEntityFieldValue(Object entity, String fieldName, String entityType) {
        if (entity == null || fieldName == null) {
            return null;
        }

        try {
            return extractFieldValueUsingReflection(entity, fieldName);
        } catch (Exception e) {
            log.warn("Failed to extract field '{}' from entity type '{}': {}", fieldName, entityType, e.getMessage());
            return null;
        }
    }

    /**
     * Extract field value using reflection and handle nested fields
     */
    private static Object extractFieldValueUsingReflection(Object entity, String fieldName) {
        if (entity == null || fieldName == null) {
            return null;
        }

        try {
            // Handle nested field paths (e.g., "contactInfo.givenName", "address.city")
            if (fieldName.contains(".")) {
                return extractNestedFieldValue(entity, fieldName);
            }

            // Handle simple field names
            return extractSimpleFieldValue(entity, fieldName);

        } catch (Exception e) {
            log.debug("Reflection failed for field '{}' on entity {}: {}", fieldName, entity.getClass().getSimpleName(), e.getMessage());
            return null;
        }
    }

    /**
     * Extract nested field value (e.g., "contactInfo.givenName")
     */
    private static Object extractNestedFieldValue(Object entity, String fieldPath) {
        String[] parts = fieldPath.split("\\.");
        Object currentObject = entity;

        for (String part : parts) {
            if (currentObject == null) {
                return null;
            }
            currentObject = extractSimpleFieldValue(currentObject, part);
        }

        return formatFieldValue(currentObject);
    }

    /**
     * Extract simple field value using getter methods
     */
    private static Object extractSimpleFieldValue(Object entity, String fieldName) {
        if (entity == null || fieldName == null) {
            return null;
        }

        Class<?> clazz = entity.getClass();

        // Try different getter method naming conventions
        String[] possibleGetterNames = {
            "get" + capitalize(fieldName),
            "is" + capitalize(fieldName),
            "get" + capitalize(convertSnakeToCamel(fieldName)),
            "is" + capitalize(convertSnakeToCamel(fieldName))
        };

        for (String getterName : possibleGetterNames) {
            try {
                java.lang.reflect.Method method = clazz.getMethod(getterName);
                Object value = method.invoke(entity);
                return formatFieldValue(value);
            } catch (Exception e) {
                // Continue trying other getter names
            }
        }

        // If no getter found, try direct field access
        try {
            java.lang.reflect.Field field = clazz.getDeclaredField(fieldName);
            field.setAccessible(true);
            Object value = field.get(entity);
            return formatFieldValue(value);
        } catch (Exception e) {
            // Field not found
        }

        return null;
    }

    /**
     * Format field value for export (handle dates, collections, etc.)
     */
    private static Object formatFieldValue(Object value) {
        if (value == null) {
            return null;
        }

        // Handle dates
        if (value instanceof java.time.LocalDateTime) {
            return formatDateForExport((java.time.LocalDateTime) value);
        } else if (value instanceof java.util.Date) {
            return formatDateForExport((java.util.Date) value);
        }

        // Handle collections - return first element or comma-separated string
        if (value instanceof java.util.Collection<?> collection) {
            if (collection.isEmpty()) {
                return null;
            }

            // For simple collections, join with comma
            if (collection.size() == 1) {
                return formatFieldValue(collection.iterator().next());
            } else {
                return collection.stream()
                    .map(Object::toString)
                    .collect(Collectors.joining(", "));
            }
        }

        // Handle enums
        if (value instanceof Enum<?>) {
            return value.toString();
        }

        return value;
    }

    /**
     * Capitalize first letter of string
     */
    private static String capitalize(String str) {
        if (str == null || str.isEmpty()) {
            return str;
        }
        return str.substring(0, 1).toUpperCase() + str.substring(1);
    }

    /**
     * Convert snake_case to camelCase
     */
    private static String convertSnakeToCamel(String snakeCase) {
        if (snakeCase == null || snakeCase.isEmpty()) {
            return snakeCase;
        }

        StringBuilder camelCase = new StringBuilder();
        boolean capitalizeNext = false;

        for (char c : snakeCase.toCharArray()) {
            if (c == '_') {
                capitalizeNext = true;
            } else if (capitalizeNext) {
                camelCase.append(Character.toUpperCase(c));
                capitalizeNext = false;
            } else {
                camelCase.append(Character.toLowerCase(c));
            }
        }

        return camelCase.toString();
    }

    /**
     * Validate and filter requested fields against SettingsEntity metadata
     */
    public static List<String> validateAndFilterFields(
            List<String> requestedFields,
            String entityType,
            String instanceId,
            Map<String, FieldMetadata> fieldMetadataMap) {

        if (requestedFields == null || requestedFields.isEmpty()) {
            log.info("No fields requested, using default fields for entity: {}", entityType);
            return getDefaultFields(entityType, instanceId);
        }

        List<String> validFields = new ArrayList<>();
        List<String> invalidFields = new ArrayList<>();

        for (String field : requestedFields) {
            if (fieldMetadataMap.containsKey(field)) {
                validFields.add(field);
            } else {
                invalidFields.add(field);
            }
        }

        if (!invalidFields.isEmpty()) {
            log.warn("Invalid fields found for entity {}: {}. Available fields: {}",
                entityType, invalidFields, fieldMetadataMap.keySet());
        }

        log.info("Validated {} out of {} requested fields for entity {}: {}",
            validFields.size(), requestedFields.size(), entityType, validFields);

        return validFields;
    }

    /**
     * Format field value according to SettingsEntity metadata type
     */
    public static Object formatFieldValueByType(Object value, FieldMetadata metadata) {
        if (value == null || metadata == null) {
            return formatFieldValue(value);
        }

        String type = metadata.getType();
        if (type == null) {
            return formatFieldValue(value);
        }

        try {
            switch (type.toLowerCase()) {
                case "bigdecimal", "decimal", "number":
                    if (value instanceof Number) {
                        return ((Number) value).doubleValue();
                    }
                    break;

                case "date", "datetime", "localdatetime":
                    return formatDateForExport(value);

                case "boolean":
                    if (value instanceof Boolean) {
                        return value;
                    }
                    break;

                case "list", "array":
                    if (value instanceof Collection<?> collection) {
                        if (collection.isEmpty()) {
                            return null;
                        }
                        return collection.stream()
                            .map(Object::toString)
                            .collect(Collectors.joining(", "));
                    }
                    break;

                case "string":
                default:
                    return value != null ? value.toString() : null;
            }
        } catch (Exception e) {
            log.warn("Error formatting field value by type {}: {}", type, e.getMessage());
        }

        // Fallback to default formatting
        return formatFieldValue(value);
    }

    /**
     * Create export summary with field metadata information
     */
    public static void logExportSummaryWithMetadata(
            String entityType,
            String instanceId,
            String owner,
            int recordCount,
            String fileName,
            List<String> exportedFields,
            List<String> arrayFields) {

        log.info("Export Summary - Entity: {}, Instance: {}, Owner: {}, Records: {}, File: {}",
                entityType, instanceId, owner, recordCount, fileName);
        log.info("Exported Fields ({}): {}", exportedFields.size(), exportedFields);

        if (!arrayFields.isEmpty()) {
            log.info("Array Fields Expanded ({}): {}", arrayFields.size(), arrayFields);
        }
    }






}
