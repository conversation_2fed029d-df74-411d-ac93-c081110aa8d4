package it.circle.plugin.repository;

import it.circle.crm.data.settings.entity.document.SettingsEntityDocument;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface SettingsEntityRepository extends MongoRepository<SettingsEntityDocument, String> {
    
    /**
     * Find settings entity by entity name and instance ID
     */
    @Query("{'entity': ?0, 'instanceId': ?1}")
    Optional<SettingsEntityDocument> findByEntityAndInstanceId(String entity, String instanceId);
    
    /**
     * Find all settings entities for a specific instance
     */
    @Query("{'instanceId': ?0}")
    List<SettingsEntityDocument> findByInstanceId(String instanceId);
    
    /**
     * Find settings entity by entity name (for default/global settings)
     */
    @Query("{'entity': ?0}")
    List<SettingsEntityDocument> findByEntity(String entity);
}
