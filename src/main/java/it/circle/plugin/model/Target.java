package it.circle.plugin.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class Target {
    
    @JsonProperty("entity")
    private String entity;
    
    @JsonProperty("fields")
    private Fields fields;
    
    @JsonProperty("filters")
    private List<Filter> filters;
}
