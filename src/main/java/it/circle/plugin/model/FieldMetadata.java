package it.circle.plugin.model;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * Represents metadata for a field in an entity, including type information and nested structure
 * This class is used to store field definitions retrieved from SettingsEntity collection
 * and supports the advanced Excel export workflow with array expansion.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FieldMetadata {
    
    /**
     * Field name (e.g., "_id", "total_products_tax_incl", "OrderRow")
     */
    private String name;
    
    /**
     * Field type (e.g., "ObjectId", "BigDecimal", "String", "List", "Object")
     */
    private String type;
    
    /**
     * For array/list fields, contains metadata for child fields
     * For object fields, contains metadata for nested properties
     */
    private List<FieldMetadata> children;
    
    /**
     * Additional metadata properties (format, validation rules, etc.)
     */
    private Map<String, Object> properties;
    
    /**
     * Check if this field is an array/list type
     */
    public boolean isArrayType() {
        return "List".equalsIgnoreCase(type) || "Array".equalsIgnoreCase(type);
    }
    
    /**
     * Check if this field is an object type
     */
    public boolean isObjectType() {
        return "Object".equalsIgnoreCase(type) || "Document".equalsIgnoreCase(type);
    }
    
    /**
     * Check if this field is a primitive type
     */
    public boolean isPrimitiveType() {
        return !isArrayType() && !isObjectType();
    }
    
    /**
     * Get child field by name
     */
    public FieldMetadata getChildField(String childName) {
        if (children == null) {
            return null;
        }
        
        return children.stream()
                .filter(child -> childName.equals(child.getName()))
                .findFirst()
                .orElse(null);
    }
    
    /**
     * Check if this field has children
     */
    public boolean hasChildren() {
        return children != null && !children.isEmpty();
    }
    
    /**
     * Get the full field path for nested fields
     * E.g., for OrderRow.product_name, returns "OrderRow.product_name"
     */
    public String getFullPath(String parentPath) {
        if (parentPath == null || parentPath.isEmpty()) {
            return name;
        }
        return parentPath + "." + name;
    }
    
    /**
     * Create a simple primitive field metadata
     */
    public static FieldMetadata createPrimitive(String name, String type) {
        return new FieldMetadata(name, type, null, null);
    }
    
    /**
     * Create an array field metadata with children
     */
    public static FieldMetadata createArray(String name, List<FieldMetadata> children) {
        return new FieldMetadata(name, "List", children, null);
    }
    
    /**
     * Create an object field metadata with children
     */
    public static FieldMetadata createObject(String name, List<FieldMetadata> children) {
        return new FieldMetadata(name, "Object", children, null);
    }
    
    /**
     * Create field metadata with properties
     */
    public static FieldMetadata createWithProperties(String name, String type, Map<String, Object> properties) {
        return new FieldMetadata(name, type, null, properties);
    }
    
    /**
     * Create complex field metadata with children and properties
     */
    public static FieldMetadata createComplex(String name, String type, List<FieldMetadata> children, Map<String, Object> properties) {
        return new FieldMetadata(name, type, children, properties);
    }
    
    /**
     * Get property value by key
     */
    public Object getProperty(String key) {
        if (properties == null) {
            return null;
        }
        return properties.get(key);
    }
    
    /**
     * Check if property exists
     */
    public boolean hasProperty(String key) {
        return properties != null && properties.containsKey(key);
    }
    
    /**
     * Get property as string
     */
    public String getPropertyAsString(String key) {
        Object value = getProperty(key);
        return value != null ? value.toString() : null;
    }
    
    /**
     * Get property as boolean
     */
    public boolean getPropertyAsBoolean(String key, boolean defaultValue) {
        Object value = getProperty(key);
        if (value instanceof Boolean) {
            return (Boolean) value;
        }
        if (value instanceof String) {
            return Boolean.parseBoolean((String) value);
        }
        return defaultValue;
    }
    
    /**
     * Check if field is required
     */
    public boolean isRequired() {
        return getPropertyAsBoolean("required", false);
    }
    
    /**
     * Get field format
     */
    public String getFormat() {
        return getPropertyAsString("format");
    }
    
    /**
     * Get default value
     */
    public Object getDefaultValue() {
        return getProperty("defaultValue");
    }
    
    /**
     * Get validation rules
     */
    public Object getValidation() {
        return getProperty("validation");
    }
    
    /**
     * Check if field supports array expansion for Excel export
     */
    public boolean supportsArrayExpansion() {
        return isArrayType() && hasChildren();
    }
    
    /**
     * Get display name for Excel headers
     */
    public String getDisplayName() {
        String displayName = getPropertyAsString("displayName");
        if (displayName != null && !displayName.trim().isEmpty()) {
            return displayName;
        }
        
        // Convert field name to readable format
        return formatFieldNameForDisplay(name);
    }
    
    /**
     * Format field name for display (convert camelCase to Title Case)
     */
    private String formatFieldNameForDisplay(String fieldName) {
        if (fieldName == null || fieldName.isEmpty()) {
            return fieldName;
        }
        
        // Convert camelCase to Title Case
        String formatted = fieldName.replaceAll("([a-z])([A-Z])", "$1 $2");
        
        // Capitalize first letter of each word
        String[] words = formatted.split(" ");
        StringBuilder result = new StringBuilder();
        
        for (String word : words) {
            if (!word.isEmpty()) {
                result.append(Character.toUpperCase(word.charAt(0)))
                      .append(word.substring(1).toLowerCase())
                      .append(" ");
            }
        }
        
        return result.toString().trim();
    }
    
    /**
     * Create a copy of this field metadata
     */
    public FieldMetadata copy() {
        List<FieldMetadata> copiedChildren = null;
        if (children != null) {
            copiedChildren = children.stream()
                    .map(FieldMetadata::copy)
                    .toList();
        }
        
        Map<String, Object> copiedProperties = null;
        if (properties != null) {
            copiedProperties = Map.copyOf(properties);
        }
        
        return new FieldMetadata(name, type, copiedChildren, copiedProperties);
    }
    
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("FieldMetadata{");
        sb.append("name='").append(name).append('\'');
        sb.append(", type='").append(type).append('\'');
        if (children != null && !children.isEmpty()) {
            sb.append(", children=").append(children.size()).append(" items");
        }
        if (properties != null && !properties.isEmpty()) {
            sb.append(", properties=").append(properties.keySet());
        }
        sb.append('}');
        return sb.toString();
    }
}
