package it.circle.plugin.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ExportMessage {
    
    @JsonProperty("instanceId")
    private String instanceId;
    
    @JsonProperty("owner")
    private String owner;

    @JsonProperty("companyName")
    private String companyName;

    @JsonProperty("exportId")
    private String exportId;
    
    @JsonProperty("targets")
    private List<Target> targets;
}
