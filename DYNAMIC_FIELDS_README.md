# Dynamic Field System for Entity Export

## Overview

This document describes the implementation of a dynamic field system that retrieves entity field definitions from MongoDB's SettingsEntity collection instead of using hard-coded field lists.

## Problem Solved

Previously, the export system used hard-coded field lists for each entity type, which had several issues:
- Fields could become outdated when entity structures changed
- New fields wouldn't be automatically available for export
- Risk of missing fields or using incorrect field names
- Maintenance overhead when entity structures evolved

**Note**: We have completely eliminated hard-coded field extraction methods (like `getOrderFieldValue`, `getContactFieldValue`) and replaced them with a generic reflection-based approach that can extract field values from any document object. This makes the system truly dynamic and eliminates the need to maintain entity-specific field extraction logic.

## Solution Architecture

### 1. SettingsEntityRepository
- **Location**: `src/main/java/it/circle/plugin/repository/SettingsEntityRepository.java`
- **Purpose**: Provides access to the SettingsEntity collection in MongoDB
- **Key Methods**:
  - `findByEntityAndInstanceId()`: Find entity-specific settings
  - `findByEntity()`: Find global entity settings
  - `findByInstanceId()`: Find all settings for an instance

### 2. FieldMetadataService
- **Location**: `src/main/java/it/circle/plugin/service/FieldMetadataService.java`
- **Purpose**: Retrieves and caches field definitions from SettingsEntity
- **Key Features**:
  - Caches field definitions for performance (`@Cacheable`)
  - Falls back to hard-coded defaults if SettingsEntity is unavailable
  - Extracts fields from both EntityEdit and EntityList configurations
  - Provides field metadata for validation and mapping

### 3. FieldMappingUtility
- **Location**: `src/main/java/it/circle/plugin/utility/FieldMappingUtility.java`
- **Purpose**: Handles field name variations and aliases
- **Key Features**:
  - Maps common field name variations (e.g., "orderid" → "order_id")
  - Provides field name normalization
  - Finds best matching fields from available options
  - Supports field name equivalence checking

### 4. FieldValidationService
- **Location**: `src/main/java/it/circle/plugin/service/FieldValidationService.java`
- **Purpose**: Validates requested fields against available fields
- **Key Features**:
  - Validates field lists against SettingsEntity definitions
  - Provides suggestions for invalid field names
  - Returns detailed validation results with valid/invalid field lists

### 5. Updated ExportUtility
- **Location**: `src/main/java/it/circle/plugin/utility/ExportUtility.java`
- **Changes**:
  - Now uses `FieldMetadataService` for dynamic field retrieval
  - Updated method signatures to include `instanceId` parameter
  - Integrates with `FieldMappingUtility` for field name normalization
  - **Completely removed hard-coded field extraction methods**
  - **Added generic reflection-based field extraction** that works with any document object
  - Supports nested field paths (e.g., "contactInfo.givenName", "address.city")
  - Handles collections, dates, enums automatically
  - Maintains backward compatibility with fallback defaults

## Usage Examples

### Basic Usage (Automatic Field Detection)
```java
// Fields are automatically retrieved from SettingsEntity
List<String> fields = fieldMetadataService.getEntityFields("contact", "instance123");
```

### Field Validation
```java
List<String> requestedFields = Arrays.asList("firstName", "lastName", "invalidField");
FieldValidationResult result = fieldValidationService.validateFields(
    requestedFields, "contact", "instance123");

if (result.hasInvalidFields()) {
    log.warn("Invalid fields: {}", result.getInvalidFields());
    log.info("Suggestions: {}", result.getFieldSuggestions());
}
```

### Field Name Mapping
```java
// Normalize field names to handle variations
String normalized = FieldMappingUtility.normalizeFieldName("firstName"); // → "first_name"
boolean equivalent = FieldMappingUtility.areFieldNamesEquivalent("orderid", "order_id"); // → true
```

## Configuration

### Cache Configuration
- **Location**: `src/main/java/it/circle/plugin/config/CacheConfig.java`
- **Caches**:
  - `entityFields`: Caches field lists per entity/instance
  - `fieldMetadata`: Caches individual field metadata

### Field Aliases
Field aliases are configured in `FieldMappingUtility` and include common variations:
- Order fields: `order_id`, `orderid`, `_id`, `id`
- Contact fields: `first_name`, `firstName`, `firstname`
- Email fields: `email`, `email_address`, `primary_email`
- Date fields: `created`, `created_date`, `createddate`

## Migration Guide

### For Service Implementations
Update method calls to include `instanceId`:
```java
// Before
List<Map<String, Object>> exportData = ExportUtility.convertEntityToExportData(
    entities, requestedFields, "contact");

// After
List<Map<String, Object>> exportData = ExportUtility.convertEntityToExportData(
    entities, requestedFields, "contact", instanceId);
```

### For Field Validation
Add field validation to service implementations:
```java
// Validate requested fields
FieldValidationService.FieldValidationResult validation = 
    fieldValidationService.validateFields(requestedFields, entityType, instanceId);

if (validation.hasInvalidFields()) {
    log.warn("Invalid fields detected: {}", validation.getValidationSummary());
}

// Use only valid fields
requestedFields = validation.getValidFields();
```

## Benefits

1. **Dynamic Field Discovery**: Fields are automatically discovered from SettingsEntity
2. **Better Error Handling**: Invalid fields are detected with helpful suggestions
3. **Field Name Flexibility**: Supports multiple naming conventions and aliases
4. **Performance**: Caching reduces database queries
5. **Backward Compatibility**: Falls back to hard-coded defaults if needed
6. **Maintainability**: No need to update code when entity structures change

## Testing

### Unit Tests
- `FieldMetadataServiceTest`: Tests dynamic field retrieval
- `FieldMappingUtilityTest`: Tests field name mapping and normalization
- `FieldValidationServiceTest`: Tests field validation logic

### Integration Testing
To test the complete flow:
1. Set up SettingsEntity documents in test database
2. Verify field retrieval works correctly
3. Test field validation and mapping
4. Verify export functionality with dynamic fields

## Troubleshooting

### Common Issues

1. **No fields returned**: Check if SettingsEntity exists for the entity/instance
2. **Cache issues**: Clear cache or restart application
3. **Field mapping problems**: Check field aliases in `FieldMappingUtility`
4. **Performance issues**: Monitor cache hit rates and database queries

### Logging
Enable debug logging for detailed information:
```yaml
logging:
  level:
    it.circle.plugin.service.FieldMetadataService: DEBUG
    it.circle.plugin.service.FieldValidationService: DEBUG
```

## Future Enhancements

1. **Dynamic Field Types**: Support for field type information from SettingsEntity
2. **Field Permissions**: Respect field-level permissions from SettingsEntity
3. **Custom Field Mappings**: Allow custom field mapping configurations
4. **Field Validation Rules**: Support for field validation rules from SettingsEntity
5. **Real-time Updates**: Automatically refresh cache when SettingsEntity changes
